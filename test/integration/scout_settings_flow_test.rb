require 'test_helper'

class ScoutSettingsFlowTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: 'Flow',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization =
      Organization.create!(
        name: 'Flow Test Organization',
        bio: 'Test organization for flow testing',
        email: '<EMAIL>',
      )

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'complete settings navigation flow' do
    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }
    assert_redirected_to '/launchpad'

    # Navigate to settings
    get scout_settings_path
    assert_response :success
    assert_select 'h1', text: 'Settings'

    # Test General tab
    assert_select 'nav a[href="' + scout_settings_path + '"]', text: 'General'
    assert_select 'h2', text: 'Personal Profile'

    # Test Account tab navigation
    get scout_settings_account_path
    assert_response :success
    assert_select 'h2', text: 'Password Management'

    # Test Billing tab navigation
    get scout_settings_subscription_path
    assert_response :success
    assert_select 'h2', text: 'Subscription Management'

    # Test Organization tab navigation
    get scout_settings_organization_path
    assert_response :success
    assert_select 'h2', text: 'Organization Information'
  end

  test 'user profile update flow with validation' do
    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Go to settings
    get scout_settings_path
    assert_response :success

    # Submit invalid data
    patch scout_settings_path,
          params: {
            user: {
              first_name: '', # Invalid
              last_name: 'Updated',
            },
          }

    assert_response :unprocessable_entity
    assert_select '.text-red-800', text: /error.*prohibited/

    # Submit valid data
    patch scout_settings_path,
          params: {
            user: {
              first_name: 'Updated',
              last_name: 'Name',
            },
          }

    assert_redirected_to scout_settings_path
    follow_redirect!
    assert_select '.text-green-800', text: 'Profile updated successfully.'

    # Verify data was updated
    @scout.reload
    assert_equal 'Updated', @scout.first_name
    assert_equal 'Name', @scout.last_name
  end

  test 'organization update flow with validation' do
    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Go to organization settings
    get scout_settings_organization_path
    assert_response :success

    # Submit invalid data
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: '', # Invalid
              email: 'invalid-email', # Invalid
            },
          }

    assert_response :unprocessable_entity
    assert_select '.text-red-800', text: /error.*prohibited/
    assert_select '.text-red-600', text: /Name can't be blank/
    assert_select '.text-red-600', text: /Email is invalid/

    # Submit valid data
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: 'Updated Organization',
              bio: 'Updated bio content',
              email: '<EMAIL>',
            },
          }

    assert_redirected_to scout_settings_organization_path
    follow_redirect!
    assert_select '.text-green-800',
                  text: 'Organization settings updated successfully.'

    # Verify data was updated
    @organization.reload
    assert_equal 'Updated Organization', @organization.name
    assert_equal 'Updated bio content', @organization.bio
    assert_equal '<EMAIL>', @organization.email
  end

  test 'logout all sessions flow' do
    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Create additional session
    other_session =
      @scout.sessions.create!(
        ip_address: '***********',
        user_agent: 'Other Browser',
      )

    # Go to account settings
    get scout_settings_account_path
    assert_response :success

    # Logout all sessions
    delete scout_settings_logout_all_path

    assert_redirected_to scout_settings_path
    follow_redirect!
    assert_select '.text-green-800',
                  text:
                    'Successfully logged out from all other devices and sessions.'

    # Verify other session was destroyed
    assert_not Session.exists?(other_session.id)
  end

  test 'settings access without organization membership' do
    # Remove organization membership
    @scout.organization_memberships.destroy_all

    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Try to access settings
    get scout_settings_path

    assert_redirected_to scout_root_path
    follow_redirect!
    assert_select '.text-red-800',
                  text:
                    'You must be part of an organization to access settings.'
  end

  test 'tab navigation maintains state across requests' do
    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Test each tab shows correct active state
    tabs = [
      { path: scout_settings_path, name: 'General' },
      { path: scout_settings_account_path, name: 'Account' },
      { path: scout_settings_subscription_path, name: 'Billing' },
      { path: scout_settings_organization_path, name: 'Organization' },
    ]

    tabs.each do |tab|
      get tab[:path]
      assert_response :success

      # Check that the current tab is marked as active
      assert_select 'nav a[href="' + tab[:path] + '"]' do |elements|
        assert elements.any? { |el| el['class'].include?('text-stone-900') },
               "#{tab[:name]} tab should be active when visiting #{tab[:path]}"
      end
    end
  end

  test 'form submission preserves tab context on validation errors' do
    # Sign in
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }

    # Submit invalid user data from General tab
    patch scout_settings_path, params: { user: { first_name: '' } }

    assert_response :unprocessable_entity

    # Should still be on General tab with errors displayed
    assert_select 'h2', text: 'Personal Profile'
    assert_select '.text-red-800', text: /error.*prohibited/

    # Submit invalid organization data from Organization tab
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: '',
            },
          }

    assert_response :unprocessable_entity

    # Should still be on Organization tab with errors displayed
    assert_select 'h2', text: 'Organization Information'
    assert_select '.text-red-800', text: /error.*prohibited/
  end
end
