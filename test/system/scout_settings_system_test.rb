require 'application_system_test_case'

class ScoutSettingsSystemTest < ApplicationSystemTestCase
  def setup
    # Generate unique email suffix to avoid conflicts in parallel tests
    @email_suffix = Time.current.to_f.to_s.gsub('.', '')

    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: "scout_system_test#{@email_suffix}@example.com",
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: 'System',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization =
      Organization.create!(
        name: 'System Test Organization',
        bio: 'Test organization for system testing',
        email: '<EMAIL>',
        address_line_1: '123 System St',
        city: 'System City',
        state_province: 'System State',
        postal_code: '12345',
        country: 'System Country',
      )

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  def sign_in_as_scout
    visit sign_in_path
    fill_in 'Email', with: @scout.email
    fill_in 'Password', with: 'Secret1*3*5*'
    click_button 'Log in'

    # Wait for successful login
    assert_text 'LAUNCHPAD', wait: 10
  end

  test 'complete settings interface navigation' do
    sign_in_as_scout

    # Navigate to settings from user menu or direct URL
    visit scout_settings_path

    # Verify settings page loads
    assert_text 'Settings'
    assert_text 'Manage your organization settings and information.'

    # Test tab navigation
    assert_text 'General'
    assert_text 'Account'
    assert_text 'Billing'
    assert_text 'Organization'

    # Test General tab (default)
    assert_text 'Personal Profile'
    assert_field 'First name', with: 'Scout'
    assert_field 'Last name', with: 'System'

    # Navigate to Account tab
    click_link 'Account'
    assert_text 'Password Management'
    assert_text 'Email Management'
    assert_text 'Active Sessions'

    # Navigate to Billing tab
    click_link 'Billing'
    assert_text 'Subscription Management'

    # Navigate to Organization tab
    click_link 'Organization'
    assert_text 'Organization Information'
    assert_field 'Organization Name', with: 'System Test Organization'
    assert_field 'Organization Email', with: '<EMAIL>'
  end

  test 'user profile update with validation' do
    sign_in_as_scout
    visit scout_settings_path

    # Test validation error
    fill_in 'First name', with: ''
    click_button 'Update Profile'

    # Should show validation error
    assert_text 'error prohibited your profile from being saved'
    assert_text "First name can't be blank"

    # Test successful update
    fill_in 'First name', with: 'Updated'
    fill_in 'Last name', with: 'Name'
    click_button 'Update Profile'

    # Should show success message
    assert_text 'Profile updated successfully'

    # Verify data was updated
    assert_field 'First name', with: 'Updated'
    assert_field 'Last name', with: 'Name'
  end

  test 'organization settings update with validation' do
    sign_in_as_scout
    visit scout_settings_organization_path

    # Test validation error
    fill_in 'Organization Name', with: ''
    fill_in 'Organization Email', with: 'invalid-email'
    click_button 'Update Organization'

    # Should show validation errors
    assert_text 'error prohibited your organization from being saved'
    assert_text "Name can't be blank"
    assert_text 'Email is invalid'

    # Test successful update
    fill_in 'Organization Name', with: 'Updated Organization'
    fill_in 'Organization Email', with: '<EMAIL>'
    fill_in 'Organization Bio', with: 'Updated bio content'
    click_button 'Update Organization'

    # Should show success message
    assert_text 'Organization settings updated successfully'

    # Verify data was updated
    assert_field 'Organization Name', with: 'Updated Organization'
    assert_field 'Organization Email', with: '<EMAIL>'
    assert_field 'Organization Bio', with: 'Updated bio content'
  end

  test 'responsive tab navigation' do
    sign_in_as_scout
    visit scout_settings_path

    # Test desktop navigation (default)
    assert_selector 'nav a', text: 'General'
    assert_selector 'nav a', text: 'Account'
    assert_selector 'nav a', text: 'Billing'
    assert_selector 'nav a', text: 'Organization'

    # Resize to mobile viewport
    page.driver.browser.manage.window.resize_to(375, 667)

    # Should still have navigation (may be different layout)
    assert_text 'General'
    assert_text 'Account'
    assert_text 'Billing'
    assert_text 'Organization'
  end

  test 'form loading states' do
    sign_in_as_scout
    visit scout_settings_path

    # Fill in form
    fill_in 'First name', with: 'Loading'
    fill_in 'Last name', with: 'Test'

    # Click submit and check for loading state
    # Note: This test may be timing-dependent and might need adjustment
    click_button 'Update Profile'

    # The form should show loading state briefly
    # This is hard to test reliably in system tests due to timing
    # but we can at least verify the form submits successfully
    assert_text 'Profile updated successfully', wait: 5
  end

  test 'field-level validation styling' do
    sign_in_as_scout
    visit scout_settings_path

    # Submit form with invalid data
    fill_in 'First name', with: ''
    click_button 'Update Profile'

    # Check for error styling on invalid field
    assert_selector 'input[name="user[first_name]"].ring-red-300'
    assert_text "First name can't be blank"

    # Fix the error and verify styling changes
    fill_in 'First name', with: 'Valid'
    click_button 'Update Profile'

    # Should show success and remove error styling
    assert_text 'Profile updated successfully'
    assert_no_selector 'input[name="user[first_name]"].ring-red-300'
  end

  test 'organization address form fields' do
    sign_in_as_scout
    visit scout_settings_organization_path

    # Verify all address fields are present
    assert_field 'Address Line 1'
    assert_field 'Address Line 2'
    assert_field 'City'
    assert_field 'State/Province'
    assert_field 'Postal Code'
    assert_field 'Country'

    # Test updating address
    fill_in 'Address Line 1', with: '456 Updated St'
    fill_in 'City', with: 'Updated City'
    fill_in 'State/Province', with: 'Updated State'
    fill_in 'Postal Code', with: '54321'
    fill_in 'Country', with: 'Updated Country'

    click_button 'Update Organization'

    assert_text 'Organization settings updated successfully'

    # Verify address was updated
    assert_field 'Address Line 1', with: '456 Updated St'
    assert_field 'City', with: 'Updated City'
    assert_field 'State/Province', with: 'Updated State'
    assert_field 'Postal Code', with: '54321'
    assert_field 'Country', with: 'Updated Country'
  end
end
