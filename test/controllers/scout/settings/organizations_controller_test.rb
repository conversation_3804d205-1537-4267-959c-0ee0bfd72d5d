require 'test_helper'

class Scout::Settings::OrganizationsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: 'Organizations',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization =
      Organization.create!(
        name: 'Test Organization',
        bio: 'Test organization bio',
        email: '<EMAIL>',
        address_line_1: '123 Test St',
        city: 'Test City',
        state_province: 'Test State',
        postal_code: '12345',
        country: 'Test Country',
      )

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )

    sign_in_as(@scout)
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get organization settings page' do
    get scout_settings_organization_path
    assert_response :success
    assert_select 'h1', text: 'Settings'
    assert_select 'h2', text: 'Organization Information'
    assert_select 'input[name="organization[name]"]'
    assert_select 'input[name="organization[email]"]'
    assert_select 'textarea[name="organization[bio]"]'
  end

  test 'should display current organization data' do
    get scout_settings_organization_path
    assert_response :success

    assert_select 'input[name="organization[name]"][value="Test Organization"]'
    assert_select 'input[name="organization[email]"][value="<EMAIL>"]'
    assert_select 'textarea[name="organization[bio]"]',
                  text: 'Test organization bio'
    assert_select 'input[name="organization[address_line_1]"][value="123 Test St"]'
    assert_select 'input[name="organization[city]"][value="Test City"]'
    assert_select 'input[name="organization[state_province]"][value="Test State"]'
    assert_select 'input[name="organization[postal_code]"][value="12345"]'
    assert_select 'input[name="organization[country]"][value="Test Country"]'
  end

  test 'should update organization successfully' do
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: 'Updated Organization',
              bio: 'Updated bio',
              email: '<EMAIL>',
              address_line_1: '456 Updated St',
              city: 'Updated City',
              state_province: 'Updated State',
              postal_code: '54321',
              country: 'Updated Country',
            },
          }

    assert_redirected_to scout_settings_organization_path
    assert_equal 'Organization settings updated successfully.', flash[:notice]

    @organization.reload
    assert_equal 'Updated Organization', @organization.name
    assert_equal 'Updated bio', @organization.bio
    assert_equal '<EMAIL>', @organization.email
    assert_equal '456 Updated St', @organization.address_line_1
    assert_equal 'Updated City', @organization.city
    assert_equal 'Updated State', @organization.state_province
    assert_equal '54321', @organization.postal_code
    assert_equal 'Updated Country', @organization.country
  end

  test 'should handle organization validation errors' do
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: '', # Invalid - required field
              email: 'invalid-email', # Invalid format
              bio: 'a' * 1001, # Too long
            },
          }

    assert_response :unprocessable_entity
    assert_select '.text-red-800', text: /error.*prohibited/
    assert_select '.text-red-600', text: /Name can't be blank/
    assert_select '.text-red-600', text: /Email is invalid/
    assert_select '.text-red-600', text: /Bio is too long/
  end

  test 'should display field-level validation errors' do
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: '',
              email: 'invalid-email',
            },
          }

    assert_response :unprocessable_entity

    # Check for field-level error styling
    assert_select 'input[name="organization[name]"].ring-red-300'
    assert_select 'input[name="organization[email]"].ring-red-300'

    # Check for inline error messages
    assert_select 'p.text-red-600', text: /Name can't be blank/
    assert_select 'p.text-red-600', text: /Email is invalid/
  end

  test 'should require organization membership' do
    # Remove organization membership
    @scout.organization_memberships.destroy_all

    get scout_settings_organization_path

    assert_redirected_to scout_root_path
    assert_equal 'You must be part of an organization to access settings.',
                 flash[:alert]
  end

  test 'should display active tab in navigation' do
    get scout_settings_organization_path
    assert_response :success

    # Check that Organization tab is active
    assert_select 'nav a[href="' + scout_settings_organization_path +
                    '"]' do |elements|
      # The active tab should have specific styling classes
      assert elements.any? { |el| el['class'].include?('text-stone-900') }
    end
  end

  test 'should include settings form controller for loading states' do
    get scout_settings_organization_path

    assert_response :success
    assert_select 'form[data-controller="settings-form"]'
    assert_select 'input[data-settings-form-target="submit"]'
  end

  test 'should include HTML5 validation attributes' do
    get scout_settings_organization_path

    assert_response :success
    assert_select 'input[name="organization[name]"][required]'
    assert_select 'input[name="organization[name]"][maxlength="255"]'
    assert_select 'input[name="organization[email]"][maxlength="255"]'
    assert_select 'textarea[name="organization[bio]"][maxlength="1000"]'
  end

  test 'should handle partial updates' do
    patch scout_settings_organization_path,
          params: {
            organization: {
              name: 'Partially Updated',
              # Only updating name, other fields should remain unchanged
            },
          }

    assert_redirected_to scout_settings_organization_path

    @organization.reload
    assert_equal 'Partially Updated', @organization.name
    assert_equal 'Test organization bio', @organization.bio # Should remain unchanged
    assert_equal '<EMAIL>', @organization.email # Should remain unchanged
  end
end
