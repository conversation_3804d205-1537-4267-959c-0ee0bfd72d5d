require 'test_helper'

class Scout::Settings::AccountsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: 'Accounts',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization = Organization.create!(name: 'Test Organization')

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )

    sign_in_as(@scout)
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get account settings page' do
    get scout_settings_account_path
    assert_response :success
    assert_select 'h1', text: 'Settings'
    assert_select 'h2', text: 'Password Management'
    assert_select 'h2', text: 'Email Management'
    assert_select 'h2', text: 'Active Sessions'
    assert_select 'h2', text: 'Account Actions'
  end

  test 'should display current user email' do
    get scout_settings_account_path
    assert_response :success
    assert_select 'p', text: /scout_accounts_test@example\.com/
  end

  test 'should show logout all sessions button' do
    get scout_settings_account_path
    assert_response :success
    assert_select 'a[href="' + scout_settings_logout_all_path + '"]',
                  text: 'Logout All Sessions'
  end

  test 'should require organization membership' do
    # Remove organization membership
    @scout.organization_memberships.destroy_all

    get scout_settings_account_path

    assert_redirected_to scout_root_path
    assert_equal 'You must be part of an organization to access settings.',
                 flash[:alert]
  end

  test 'should display active tab in navigation' do
    get scout_settings_account_path
    assert_response :success

    # Check that Account tab is active
    assert_select 'nav a[href="' + scout_settings_account_path +
                    '"]' do |elements|
      # The active tab should have specific styling classes
      assert elements.any? { |el| el['class'].include?('text-stone-900') }
    end
  end

  test 'should include tabbed interface structure' do
    get scout_settings_account_path
    assert_response :success

    # Check for tab navigation
    assert_select 'nav a[href="' + scout_settings_path + '"]', text: 'General'
    assert_select 'nav a[href="' + scout_settings_account_path + '"]',
                  text: 'Account'
    assert_select 'nav a[href="' + scout_settings_subscription_path + '"]',
                  text: 'Billing'
    assert_select 'nav a[href="' + scout_settings_organization_path + '"]',
                  text: 'Organization'
  end

  test 'should display session information if available' do
    # Create a session for the user
    session =
      @scout.sessions.create!(
        ip_address: '***********',
        user_agent: 'Test Browser',
      )

    get scout_settings_account_path
    assert_response :success

    # Should display session information
    assert_select 'div', text: /192\.168\.1\.1/
    assert_select 'div', text: /Test Browser/
  end

  test 'should handle user without sessions gracefully' do
    # Ensure user has no sessions
    @scout.sessions.destroy_all

    get scout_settings_account_path
    assert_response :success

    # Page should still render successfully
    assert_select 'h2', text: 'Active Sessions'
  end
end
