require 'test_helper'

class Scout::SettingsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data in proper order to avoid foreign key constraints
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all

    # Create scout user with organization
    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'Secret1*3*5*',
        first_name: 'Scout',
        last_name: '<PERSON><PERSON><PERSON>',
        scout_signup_completed: true,
        verified: true,
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @organization =
      Organization.create!(
        name: 'Test Organization',
        bio: 'Test organization bio',
        email: '<EMAIL>',
        address_line_1: '123 Test St',
        city: 'Test City',
        state_province: 'Test State',
        postal_code: '12345',
        country: 'Test Country',
      )

    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )

    sign_in_as(@scout)
  end

  def teardown
    BadgeAssignment.destroy_all
    ImpersonationLog.destroy_all
    OrganizationMembership.destroy_all
    Organization.destroy_all
    User.destroy_all
  end

  test 'should get general settings page' do
    sign_in_as @scout
    get scout_settings_path
    assert_response :success
    assert_select 'h1', text: 'Settings'
    assert_select 'h2', text: 'Personal Profile'
    assert_select 'input[name="user[first_name]"]'
    assert_select 'input[name="user[last_name]"]'
  end

  test 'should update user profile successfully' do
    sign_in_as @scout
    patch scout_settings_path,
          params: {
            user: {
              first_name: 'Updated',
              last_name: 'Name',
            },
          }

    assert_redirected_to scout_settings_path
    assert_equal 'Profile updated successfully.', flash[:notice]

    @scout.reload
    assert_equal 'Updated', @scout.first_name
    assert_equal 'Name', @scout.last_name
  end

  test 'should handle user profile validation errors' do
    sign_in_as @scout
    patch scout_settings_path,
          params: {
            user: {
              first_name: '', # Invalid - required field
              last_name: 'Name',
            },
          }

    assert_response :unprocessable_entity
    assert_select '.text-red-800', text: /error.*prohibited/
    assert_select '.text-red-600', text: /First name can't be blank/
  end

  test 'should update organization settings successfully' do
    sign_in_as @scout
    patch scout_settings_path,
          params: {
            organization: {
              name: 'Updated Organization',
              bio: 'Updated bio',
              email: '<EMAIL>',
            },
          }

    assert_redirected_to scout_settings_organization_path
    assert_equal 'Organization settings updated successfully.', flash[:notice]

    @organization.reload
    assert_equal 'Updated Organization', @organization.name
    assert_equal 'Updated bio', @organization.bio
    assert_equal '<EMAIL>', @organization.email
  end

  test 'should handle organization validation errors' do
    sign_in_as @scout
    patch scout_settings_path,
          params: {
            organization: {
              name: '', # Invalid - required field
              email: 'invalid-email', # Invalid format
            },
          }

    assert_response :unprocessable_entity
    assert_select '.text-red-800', text: /error.*prohibited/
    assert_select '.text-red-600', text: /Name can't be blank/
    assert_select '.text-red-600', text: /Email is invalid/
  end

  test 'should handle invalid form submission' do
    sign_in_as @scout
    patch scout_settings_path, params: { invalid_param: 'test' }

    assert_redirected_to scout_settings_path
    assert_equal 'Invalid form submission.', flash[:alert]
  end

  test 'should logout from all sessions' do
    sign_in_as @scout
    # Create additional session
    other_session =
      @scout.sessions.create!(
        ip_address: '***********',
        user_agent: 'Test Browser',
      )

    assert_difference '@scout.sessions.count', -1 do
      delete scout_settings_logout_all_path
    end

    assert_redirected_to scout_settings_path
    assert_equal 'Successfully logged out from all other devices and sessions.',
                 flash[:notice]

    # Verify other session was destroyed but current session remains
    begin
      assert_not other_session.reload
    rescue StandardError
      true
    end # Should be destroyed
  end

  test 'should require organization membership' do
    sign_in_as @scout
    # Remove organization membership
    @scout.organization_memberships.destroy_all

    get scout_settings_path

    assert_redirected_to scout_root_path
    assert_equal 'You must be part of an organization to access settings.',
                 flash[:alert]
  end

  test 'should display form with current user data' do
    get scout_settings_path

    assert_response :success
    assert_select 'input[name="user[first_name]"][value="Scout"]'
    assert_select 'input[name="user[last_name]"][value="Settings"]'
  end

  test 'should include settings form controller for loading states' do
    get scout_settings_path

    assert_response :success
    assert_select 'form[data-controller="settings-form"]'
    assert_select 'input[data-settings-form-target="submit"]'
  end
end
