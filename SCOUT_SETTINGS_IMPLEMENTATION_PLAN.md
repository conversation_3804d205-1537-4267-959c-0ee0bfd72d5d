# Scout Settings Enhancement Implementation Plan

## GitHub Issue #95: 🔧 Enhance Scout Settings: Add Billing, Profile Management, and Account Features

### Problem Statement

Transform scout settings from a static mockup into a fully functional, comprehensive settings interface that mirrors the sophistication of talent settings while addressing scout-specific needs.

### Current Issues

- Settings page displays hardcoded template data instead of actual user/organization information
- No billing management capabilities (unlike talent side which has comprehensive billing)
- Missing profile customization options (avatar, name changes, personal info)
- No account management features (logout all sessions, security settings)
- Organization owners cannot access organization-specific settings
- Static HTML with no form functionality or database integration

## Implementation Plan

### Phase 1: Core Functionality Setup

**Goal:** Establish the foundation for functional scout settings by fixing controller implementation and database integration

#### Task 1: Analyze Current Scout Settings Implementation

- [x] Review existing scout settings controller structure
- [x] Examine current views and identify hardcoded data
- [x] Analyze organization and user models for missing fields
- [x] Review routes configuration
- [x] Compare with talent settings implementation for reference

#### Task 2: Create Database Migration for Missing Organization Fields

- [x] Add bio field (text)
- [x] Add email field (string)
- [x] Add address_line_1 field (string)
- [x] Add address_line_2 field (string)
- [x] Add city field (string)
- [x] Add state_province field (string)
- [x] Add postal_code field (string)
- [x] Add country field (string)
- [x] Add show_email_on_profile field (boolean, default: false)

#### Task 3: Fix Scout Settings Controller Implementation

- [x] Properly initialize @user and @current_organization
- [x] Implement update action with form handling
- [x] Add proper parameter filtering
- [x] Implement validation and error handling
- [x] Add success/error flash messages

#### Task 4: Update Organization Model with New Fields and Validations

- [x] Add validations for new fields
- [x] Add any necessary methods for settings functionality
- [x] Ensure proper relationships are maintained

#### Task 5: Convert Static Settings View to Functional Forms

- [x] Replace hardcoded template data with actual data binding
- [x] Implement working forms with proper field mapping
- [x] Add form validation and error display
- [x] Ensure responsive design

### Phase 2: Advanced Features Implementation

**Goal:** Add billing management, profile customization, and account management features

#### Task 6: Implement Billing Management for Scout Organizations

- [x] Create subscription management interface
- [x] Mirror talent side billing functionality
- [x] Integrate Stripe customer portal
- [x] Display subscription status and features
- [x] Add billing history and payment methods access
- [x] Fix routing structure for subscription settings
- [x] Resolve helper method access in controllers
- [x] Update subscription plan availability for scouts
- [x] Create migration to enable scout subscription plans

#### Task 7: Add Profile Customization Features

- [ ] Implement avatar upload using Active Storage
- [ ] Add name change functionality
- [ ] Create personal information management
- [ ] Add timezone and preference settings

#### Task 8: Implement Account Management Features

- [ ] Add password change functionality with current password verification
- [ ] Implement "logout all sessions" feature
- [ ] Add security settings interface
- [ ] Create account deletion/deactivation options

### Phase 3: UI/UX Enhancement

**Goal:** Implement tabbed interface and ensure consistent styling with existing design system

#### Task 9: Create Tabbed Interface Structure

- [ ] Implement organized navigation with tabs
- [ ] Create General settings tab
- [ ] Create Billing settings tab
- [ ] Create Account settings tab
- [ ] Create Organization settings tab (for owners)
- [ ] Add proper routing for each tab

#### Task 10: Ensure Design System Consistency

- [ ] Apply stone color palette throughout
- [ ] Match styling patterns from talent settings
- [ ] Ensure consistent typography and spacing
- [ ] Implement proper card layouts and form styling
- [ ] Add responsive design for mobile and desktop

#### Task 11: Add Comprehensive Error Handling and User Feedback

- [ ] Implement form validation with clear error messages
- [ ] Add success/error feedback after form submissions
- [ ] Create loading states for form submissions
- [ ] Add confirmation dialogs for destructive actions
- [ ] Implement proper error boundaries

### Phase 4: Testing and Quality Assurance

**Goal:** Create comprehensive tests and perform manual testing to ensure all functionality works correctly

#### Task 12: Write Comprehensive Test Suite

- [ ] Create unit tests for settings controller actions
- [ ] Write integration tests for form submissions and data persistence
- [ ] Implement system tests for tabbed navigation and user flows
- [ ] Add feature tests for billing integration
- [ ] Create security tests for role-based access

## Technical Implementation Details

### Database Schema Updates

```ruby
# Migration: AddMissingFieldsToOrganizations
add_column :organizations, :bio, :text
add_column :organizations, :email, :string
add_column :organizations, :address_line_1, :string
add_column :organizations, :address_line_2, :string
add_column :organizations, :city, :string
add_column :organizations, :state_province, :string
add_column :organizations, :postal_code, :string
add_column :organizations, :country, :string
add_column :organizations, :show_email_on_profile, :boolean, default: false
```

### Files to Modify/Create

#### Controllers

- `app/controllers/scout/settings_controller.rb` - Fix and enhance main controller
- `app/controllers/scout/settings/passwords_controller.rb` - New password management
- `app/controllers/scout/settings/subscriptions_controller.rb` - New billing management
- `app/controllers/scout/settings/organization_controller.rb` - New organization settings

#### Views

- `app/views/scout/settings/show.html.erb` - Convert from static to functional
- `app/views/scout/settings/_shell.html.erb` - New tabbed interface shell
- `app/views/scout/settings/_tabbar.html.erb` - New tab navigation
- `app/views/scout/settings/passwords/show.html.erb` - New password management view
- `app/views/scout/settings/subscriptions/show.html.erb` - New billing view

#### Models

- `app/models/organization.rb` - Add new fields and validations
- `app/models/user.rb` - Enhance user settings capabilities

#### Routes

- `config/routes.rb` - Add nested settings routes

#### JavaScript

- `app/javascript/controllers/scout_settings_controller.js` - New Stimulus controller

## Acceptance Criteria

### Core Functionality

- [x] Scout settings controller properly initializes and processes form data
- [x] Organization information displays actual data and can be updated
- [x] User personal information can be viewed and modified
- [x] Form validation provides clear error messages
- [x] Success/error feedback appears after form submissions

### Billing Management

- [x] Scouts can view current subscription/billing information
- [x] Billing portal integration allows plan management
- [x] Subscription status and features are clearly displayed
- [x] Billing history and payment methods are accessible

### Profile & Account Management

- [ ] Users can upload and update avatar/profile pictures
- [ ] Full name and personal information can be modified
- [ ] Password change functionality works correctly
- [ ] "Logout all sessions" feature functions properly

### UI/UX Requirements

- [ ] Tabbed navigation interface works smoothly
- [ ] Responsive design works on mobile and desktop
- [ ] Loading states and form submission feedback
- [ ] Consistent styling with talent settings patterns
- [ ] Proper error handling and user feedback

## Dependencies

- Existing Pay gem integration for billing
- Organization and User models with proper relationships
- Scout base controller authentication
- Existing talent settings implementation as reference
- File upload capabilities (Active Storage)

## Security Considerations

- Data protection for sensitive organization data
- Role-based access control for organization owners vs members
- Secure session management and logout functionality
- Input validation to prevent XSS and injection attacks
- Audit trail for organization settings changes
