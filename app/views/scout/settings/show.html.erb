<%# Container div with styling following talent settings pattern %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200">
  <div class="max-w-2xl pb-5 mb-6 border-b border-stone-200 sm:pb-0">
    <div class="mb-2 text-2xl font-semibold text-stone-900">
      Settings
    </div>
    <div class="pb-2 mb-4 text-sm text-stone-600">
      Manage your organization settings and information.
    </div>
  </div>

  <%# Personal Profile Section %>
  <%= form_with model: @user, url: scout_settings_path, method: :patch, data: { turbo: false }, class: "max-w-2xl pt-6" do |user_form| %>
    <div class="pb-12 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Personal Profile</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Update your personal information and avatar.</p>

      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <%# Avatar %>
        <div class="sm:col-span-4">
          <%= user_form.label :avatar, "Avatar", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="flex items-center mt-2 gap-x-3">
            <%# Display current avatar or placeholder %>
            <% if @user.avatar.attached? %>
              <%= image_tag @user.avatar, id: "avatar-preview", class: "h-12 w-12 rounded-full object-cover" %>
            <% else %>
              <div id="avatar-preview" class="flex items-center justify-center w-12 h-12 bg-stone-200 rounded-full">
                <span class="text-xs text-stone-500">No Photo</span>
              </div>
            <% end %>
            <%# Styled label acting as button, hiding the actual input %>
            <%= user_form.label :avatar, "Choose File", class: "cursor-pointer rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 hover:bg-stone-50" %>
            <%= user_form.file_field :avatar, id: "user_avatar", class: "sr-only", onchange: "previewImage(event, 'avatar-preview')" %>
          </div>
          <p class="mt-2 text-xs leading-5 text-stone-600">JPG, GIF or PNG. 1MB max.</p>
        </div>

        <%# First Name %>
        <div class="sm:col-span-3">
          <%= user_form.label :first_name, "First Name", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= user_form.text_field :first_name, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Last Name %>
        <div class="sm:col-span-3">
          <%= user_form.label :last_name, "Last Name", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= user_form.text_field :last_name, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>
      </div>
    </div>

    <%# Action Buttons for Personal Profile %>
    <div class="flex items-center justify-end py-4 mt-6 gap-x-6">
      <%= user_form.submit "Update Profile", class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900" %>
    </div>
  <% end %>

  <%# Display user validation errors if any %>
  <% if @user&.errors&.any? %>
    <div class="p-4 mt-6 border border-red-300 rounded-md bg-red-50">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(@user.errors.count, "error") %> prohibited your profile from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="pl-5 space-y-1 list-disc">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <%# Organization Information Section %>
  <%= form_with model: @current_organization, url: scout_settings_path, method: :patch, data: { turbo: false }, class: "max-w-2xl pt-6" do |f| %>
    <div class="pb-12 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Organization Information</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Update your organization's basic information and contact details.</p>

      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <%# Organization Name %>
        <div class="sm:col-span-4">
          <%= f.label :name, "Organization Name", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :name, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
          <p class="mt-2 text-xs leading-5 text-stone-600">This will be displayed on your public profile.</p>
        </div>

        <%# Organization Bio %>
        <div class="sm:col-span-6">
          <%= f.label :bio, "Organization Bio", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_area :bio, rows: 3, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
          <p class="mt-2 text-xs leading-5 text-stone-600">Brief description of your organization (max 1000 characters).</p>
        </div>

        <%# Organization Email %>
        <div class="sm:col-span-4">
          <%= f.label :email, "Organization Email", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.email_field :email, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
          <p class="mt-2 text-xs leading-5 text-stone-600">Contact email for your organization.</p>
        </div>

        <%# Show Email on Profile %>
        <div class="sm:col-span-6">
          <div class="flex items-center">
            <%= f.check_box :show_email_on_profile, class: "h-4 w-4 rounded border-stone-300 text-stone-900 focus:ring-stone-900" %>
            <%= f.label :show_email_on_profile, "Show email on public profile", class: "ml-3 text-sm font-medium leading-6 text-stone-900" %>
          </div>
        </div>
      </div>
    </div>

    <%# Address Section %>
    <div class="pb-12 mt-10 border-b border-stone-900/10">
      <h2 class="text-base font-semibold leading-7 text-stone-900">Address Information</h2>
      <p class="mt-1 text-sm leading-6 text-stone-600">Your organization's registered address information.</p>

      <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
        <%# Address Line 1 %>
        <div class="sm:col-span-6">
          <%= f.label :address_line_1, "Address Line 1", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :address_line_1, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Address Line 2 %>
        <div class="sm:col-span-6">
          <%= f.label :address_line_2, "Address Line 2 (Optional)", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :address_line_2, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# City %>
        <div class="sm:col-span-2">
          <%= f.label :city, "City", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :city, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# State/Province %>
        <div class="sm:col-span-2">
          <%= f.label :state_province, "State/Province", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :state_province, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Postal Code %>
        <div class="sm:col-span-2">
          <%= f.label :postal_code, "Postal Code", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :postal_code, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>

        <%# Country %>
        <div class="sm:col-span-3">
          <%= f.label :country, "Country", class: "block text-sm font-medium leading-6 text-stone-900" %>
          <div class="mt-2">
            <%= f.text_field :country, class: "block w-full rounded-md py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 sm:text-sm sm:leading-6" %>
          </div>
        </div>
      </div>
    </div>

    <%# Action Buttons %>
    <div class="flex items-center justify-end py-4 mt-6 gap-x-6">
      <%= f.submit "Save Changes", class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900" %>
    </div>
  <% end %>

  <%# Display validation errors if any %>
  <% if @current_organization&.errors&.any? %>
    <div class="p-4 mt-6 border border-red-300 rounded-md bg-red-50">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(@current_organization.errors.count, "error") %> prohibited this organization from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="pl-5 space-y-1 list-disc">
              <% @current_organization.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<%# Custom CSS to override @tailwindcss/forms focus style with higher specificity %>
<style>
  .max-w-2xl input[type=text]:focus,
  .max-w-2xl input[type=email]:focus,
  .max-w-2xl textarea:focus,
  .max-w-2xl select:focus {
    border-color: #1c1917 !important; /* stone-900 */
    box-shadow: 0 0 0 1px #1c1917 !important; /* stone-900 */
    outline: none !important;
  }
</style>

<%# JavaScript for avatar preview %>
<script>
  function previewImage(event, previewId) {
    const file = event.target.files[0];
    const preview = document.getElementById(previewId);

    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        preview.innerHTML = `<img src="${e.target.result}" class="h-12 w-12 rounded-full object-cover" alt="Avatar preview">`;
      };
      reader.readAsDataURL(file);
    }
  }
</script>
