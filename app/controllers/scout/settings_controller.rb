module Scout
  class SettingsController < Scout::BaseController
    before_action :set_user_and_organization

    def show
      # General settings tab - Personal Profile
      render 'general'
    end

    def edit
      # Edit action if needed for separate edit view
    end

    def update
      # Determine which form was submitted based on params
      if params[:user].present?
        update_user_profile
      elsif params[:organization].present?
        update_organization_settings
      else
        redirect_to scout_settings_path, alert: 'Invalid form submission.'
      end
    end

    def logout_all
      # Destroy all sessions except the current one
      @user.sessions.where.not(id: Current.session&.id).destroy_all

      redirect_to scout_settings_path,
                  notice:
                    'Successfully logged out from all other devices and sessions.'
    end

    private

    def update_user_profile
      if @user.update(user_params)
        redirect_to scout_settings_path, notice: 'Profile updated successfully.'
      else
        # Render the general tab with errors
        render 'general', status: :unprocessable_entity
      end
    end

    def update_organization_settings
      if @current_organization.update(organization_params)
        redirect_to scout_settings_organization_path,
                    notice: 'Organization settings updated successfully.'
      else
        # Render the organization tab with errors
        render 'organizations/show', status: :unprocessable_entity
      end
    end

    def set_user_and_organization
      @user = Current.user
      @current_organization = Current.organization

      # Ensure user has an organization
      unless @current_organization
        redirect_to scout_root_path,
                    alert:
                      'You must be part of an organization to access settings.'
      end
    end

    def user_params
      params.require(:user).permit(:first_name, :last_name, :avatar)
    end

    def organization_params
      params
        .require(:organization)
        .permit(
          :name,
          :bio,
          :email,
          :address_line_1,
          :address_line_2,
          :city,
          :state_province,
          :postal_code,
          :country,
          :show_email_on_profile,
        )
    end
  end
end
