# frozen_string_literal: true

module Scout
  class SubscriptionsController < Scout::BaseController
    before_action :set_user_and_organization

    def create
      plan_id = params[:plan]

      # Validate plan_id is present
      unless plan_id.present?
        redirect_to scout_settings_subscription_path, alert: 'Please select a valid subscription plan.'
        return
      end

      # Validate that the plan is available for new scout subscriptions
      unless helpers.available_scout_plans_for_new_subscriptions.key?(plan_id)
        redirect_to scout_settings_subscription_path, alert: 'The selected subscription plan is not available for scouts.'
        return
      end

      begin
        # Ensure user is set up with Stripe processor
        Current.user.set_payment_processor(:stripe)

        # Use Pay gem method to create subscription via Stripe Checkout
        checkout_session = Current.user.payment_processor.checkout(
          mode: 'subscription',
          line_items: [{ price: plan_id, quantity: 1 }],
          success_url: success_scout_subscription_url,
          cancel_url: cancel_scout_subscription_url
        )

        # Redirect to Stripe Checkout page
        redirect_to checkout_session.url, allow_other_host: true, status: :see_other

      rescue Pay::Error => e
        redirect_to cancel_scout_subscription_url, alert: "Subscription failed: #{e.message}"
      rescue StandardError => e
        # Catch other potential errors
        Rails.logger.error("Scout Subscription Error: #{e.message}\n#{e.backtrace.join("\n")}")
        redirect_to cancel_scout_subscription_url, alert: 'An unexpected error occurred during subscription.'
      end
    end

    def success
      # Optionally fetch subscription details or just show a success message
      flash[:notice] = 'Subscription successful!'
      # Redirect to settings page after showing success
      redirect_to scout_settings_subscription_path, notice: 'Subscription activated successfully!'
    end

    def cancel
      # Handle subscription cancellation/failure
      redirect_to scout_settings_subscription_path, alert: 'Subscription was cancelled or failed to process.'
    end

    private

    def set_user_and_organization
      @user = Current.user
      @current_organization = Current.organization

      # Ensure user has an organization
      unless @current_organization
        redirect_to scout_root_path,
                    alert: 'You must be part of an organization to manage subscriptions.'
      end
    end
  end
end
